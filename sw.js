const CACHE_NAME = 'mon-app-v1';
const urlsToCache = [
    '/',
    '/index_v37',
];

// Installation du service worker
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
  );
});

// Récupération des ressources
self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Cache hit - retourne la réponse
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});