export const marketplace = () => ({
  // State
  selectedBrand: 'all',
  selectedStatus: 'all',
  sortBy: 'recent',
  showDetailModal: false,
  selectedTruck: null,
  
  // Filters
  brands: ['all', 'MAN', 'Volvo', 'Mercedes-Benz', 'DAF', 'Scania', 'Iveco', 'Renault'],
  statusOptions: [
    { value: 'all', key: 'all_status' },
    { value: 'available', key: 'available' },
    { value: 'sold', key: 'sold' },
    { value: 'reserved', key: 'reserved' }
  ],
  sortOptions: [
    { value: 'recent', key: 'most_recent' },
    { value: 'price_low', key: 'price_low_high' },
    { value: 'price_high', key: 'price_high_low' },
    { value: 'year_new', key: 'year_newest' },
    { value: 'year_old', key: 'year_oldest' },
    { value: 'km_low', key: 'km_lowest' },
    { value: 'km_high', key: 'km_highest' }
  ],

  // Computed properties
  get filteredTrucks() {
    let trucks = this.$store.app.marketplaceTrucks || [];
    
    // Filter by brand
    if (this.selectedBrand !== 'all') {
      trucks = trucks.filter(truck => truck.brand === this.selectedBrand);
    }
    
    // Filter by status
    if (this.selectedStatus !== 'all') {
      trucks = trucks.filter(truck => truck.status === this.selectedStatus);
    }
    
    // Sort
    trucks = [...trucks].sort((a, b) => {
      switch (this.sortBy) {
        case 'price_low':
          return a.price - b.price;
        case 'price_high':
          return b.price - a.price;
        case 'year_new':
          return b.year - a.year;
        case 'year_old':
          return a.year - b.year;
        case 'km_low':
          return a.kilometers - b.kilometers;
        case 'km_high':
          return b.kilometers - a.kilometers;
        case 'recent':
        default:
          return new Date(b.dateAdded) - new Date(a.dateAdded);
      }
    });
    
    return trucks;
  },

  // Methods
  init() {
    // Initialize marketplace data if not already loaded
    if (!this.$store.app.marketplaceTrucks) {
      this.$store.app.initializeMarketplaceData();
    }
  },

  selectBrand(brand) {
    this.selectedBrand = brand;
  },

  selectStatus(status) {
    this.selectedStatus = status;
  },

  setSortBy(sortBy) {
    this.sortBy = sortBy;
  },

  showTruckDetails(truck) {
    this.selectedTruck = truck;
    this.showDetailModal = true;
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  },

  closeDetailModal() {
    this.showDetailModal = false;
    this.selectedTruck = null;
    // Restore body scroll
    document.body.style.overflow = '';
  },

  formatPrice(price) {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  },

  formatKilometers(km) {
    return new Intl.NumberFormat('fr-FR').format(km) + ' km';
  },

  getStatusBadgeClass(status) {
    switch (status) {
      case 'available':
        return 'bg-emerald-100 text-emerald-800';
      case 'sold':
        return 'bg-red-100 text-red-800';
      case 'reserved':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-slate-100 text-slate-800';
    }
  },

  contactSeller() {
    // This would typically open a contact form or redirect to contact page
    alert(this.$store.app.t('contact_seller_message'));
  }
});
