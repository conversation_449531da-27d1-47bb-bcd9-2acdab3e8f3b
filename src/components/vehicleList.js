import {
  getBrandLogo,
  getCountryFlag,
  parseConsignee,
} from "../services/utils.js";
export const vehicleList = () => ({
  getBrandLogo,
  getCountryFlag,
  parseConsignee,
  showVehicleDetails(vehicle) {
    this.$store.app.selectedVehicle = vehicle;
    this.$store.app.setView("detail");
  },
  getProgressPercentage(status) {
    return { BOOKED: 0, "PORT OF LOADING": 50, DEPARTED: 100 }[status] || 0;
  },
  getTimelineStepClass(currentStatus, stepStatus) {
    const currentIndex = this.$store.app.statusOrder.indexOf(currentStatus);
    const stepIndex = this.$store.app.statusOrder.indexOf(stepStatus);
    if (stepIndex < currentIndex)
      return "bg-emerald-500 text-white shadow-emerald-200";
    if (stepIndex === currentIndex)
      return "bg-blue-500 text-white shadow-blue-200 animate-pulse";
    return "bg-slate-200 text-slate-400 shadow-slate-100";
  },
  getTimelineTextClass(currentStatus, stepStatus) {
    const currentIndex = this.$store.app.statusOrder.indexOf(currentStatus);
    const stepIndex = this.$store.app.statusOrder.indexOf(stepStatus);
    return stepIndex <= currentIndex ? "text-slate-800" : "text-slate-400";
  },
});
