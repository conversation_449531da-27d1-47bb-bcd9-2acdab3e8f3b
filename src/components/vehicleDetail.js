import {
  getBrandLogo,
  getCountryFlag,
  parseConsignee,
} from "../services/utils.js";

export const vehicleDetail = () => ({
  getBrandLogo,
  getCountryFlag,
  parseConsignee,
  isStepCompleted(currentStatus, stepStatus) {
    if (!currentStatus) return false;
    const currentIndex = this.$store.app.statusOrder.indexOf(currentStatus);
    const stepIndex = this.$store.app.statusOrder.indexOf(stepStatus);
    return stepIndex < currentIndex;
  },
});
