import { getCountryFlag, formatDate } from "../services/utils.js";
export const departures = () => ({
  getCountryFlag,
  formatDate,
  get destinations() {
    return this.$store.app.destinations;
  },
  get departuresData() {
    return this.$store.app.departuresData;
  },
  selectedDestination: null,
  activeDeparture: null,
  showDestination(destination) {
    this.selectedDestination = destination;
    this.activeDeparture =
      this.departuresData[destination.name]?.[0] || null;
    this.$store.app.setView("destinationDepartures");
  },
  backToDeparturesList() {
    this.$store.app.setView("departuresList");
    this.selectedDestination = null;
    this.activeDeparture = null;
  },
  getVoyageCount(destinationName) {
    return this.departuresData[destinationName]?.length || 0;
  },
  pluralize(count, singularKey, pluralKey) {
    return count === 1
      ? this.$store.app.t(singularKey)
      : this.$store.app.t(pluralKey);
  },
  calculateVoyageDays(start, end) {
    if (!start || !end) return "N/A";
    const diff = Math.abs(new Date(end) - new Date(start));
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  },
});
