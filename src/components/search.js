import { API_BASE_URL } from "../config/appConfig.js";
export const search = () => ({
  prefix: ["S", "O", "C", "A", "R", "2", "5"],
  userInput: Array(6).fill(""),
  currentIndex: 0,
  isSearching: false,
  showError: false,
  showConnectionError: false,
  lastSearchedQuery: "",
  errorIndex: null,
  showInvalidChar: false,
  validVinChars: "ABCDEFGHJKLMNPRSTUVWXYZ0123456789",
  init() {
    this.$watch("$store.app.currentView", (v) => {
      if (v === "search") this.resetForm();
    });
  },
  get userInputString() {
    return this.userInput.join("");
  },
  resetForm() {
    this.userInput = Array(6).fill("");
    this.currentIndex = 0;
    this.showError = false;
    this.showConnectionError = false;
    this.$nextTick(() => this.$refs.hiddenInput.focus());
  },
  clearAll() {
    this.resetForm();
  },
  setCurrentIndex(index) {
    this.currentIndex = Math.max(0, Math.min(5, index));
    this.$nextTick(() => this.$refs.hiddenInput.focus());
  },
  handleInput(event) {
    const char = event.target.value.slice(-1).toUpperCase();
    if (!char) return;
    if (!this.validVinChars.includes(char)) {
      this.showInvalidChar = true;
      setTimeout(() => (this.showInvalidChar = false), 2000);
      event.target.value = "";
      return;
    }
    this.userInput[this.currentIndex] = char;
    if (this.currentIndex < 5) {
      this.currentIndex++;
    }
    event.target.value = "";
  },
  handleBackspace() {
    if (this.userInput[this.currentIndex]) {
      this.userInput[this.currentIndex] = "";
    } else if (this.currentIndex > 0) {
      this.currentIndex--;
      this.userInput[this.currentIndex] = "";
    }
    this.errorIndex = null;
  },
  handlePaste(event) {
    event.preventDefault();
    const pastedText = (event.clipboardData || window.clipboardData)
      .getData("text")
      .toUpperCase();
    let filteredText = "";
    for (const char of pastedText) {
      if (this.validVinChars.includes(char)) {
        filteredText += char;
      }
    }
    if (!filteredText) {
      this.showInvalidChar = true;
      setTimeout(() => (this.showInvalidChar = false), 2000);
      return;
    }
    for (let i = 0; i < filteredText.length && this.currentIndex + i < 6; i++) {
      this.userInput[this.currentIndex + i] = filteredText[i];
    }
    this.currentIndex = Math.min(this.currentIndex + filteredText.length, 5);
  },
  async searchVehicle() {
    if (this.userInputString.length < 6 || this.isSearching) return;
    this.isSearching = true;
    this.lastSearchedQuery = this.userInputString;
    this.showError = false;
    this.showConnectionError = false;
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/vehicules?q=${this.userInputString}`
      );
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      if (Array.isArray(data) && data.length > 0) {
        this.$store.app.vehicles = data;
        this.$store.app.setView("list");
      } else {
        this.showError = true;
      }
    } catch (error) {
      console.error("API error:", error);
      this.showConnectionError = true;
    }
    this.isSearching = false;
  },
});
