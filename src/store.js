import { translations } from './config/translations.js';
import { departuresData, destinations } from './config/departuresData.js';

export const appStore = {
    // --- STATE ---
    currentView: 'search',
    currentLang: 'fr',
    vehicles: [],
    selectedVehicle: null,
    isIdle: false,
    statusOrder: ['BOOKED', 'PORT OF LOADING', 'DEPARTED'],
    
    // --- Departures Data ---
    destinations: destinations,
    departuresData: departuresData,
    
    // --- GETTERS ---
    t(key) {
        return translations[this.currentLang]?.[key] || translations['en']?.[key] || key;
    },

    // --- ACTIONS ---
    setView(view, fromHistory = false) {
        this.currentView = view;
        if (view === 'search') {
            this.vehicles = [];
            this.selectedVehicle = null;
        }

        if (!fromHistory) {
            const url = new URL(window.location);
            url.searchParams.set('view', view);
            const title = `SOCAR - ${view.charAt(0).toUpperCase() + view.slice(1)}`;
            history.pushState({ view }, title, url);
        }
    },
    
    setLanguage(lang) {
        this.currentLang = lang;
        document.documentElement.lang = lang;
    },
};