// This file is the entry point. It imports and initializes everything.
import { appStore } from './store.js';
import { search } from './components/search.js';
import { vehicleList } from './components/vehicleList.js';
import { vehicleDetail } from './components/vehicleDetail.js';
import { departures } from './components/departures.js';
import { idle } from './components/idle.js';
import { idleService } from './services/idleService.js';

document.addEventListener('alpine:init', () => {
    Alpine.store('app', appStore);

    Alpine.data('search', search);
    Alpine.data('vehicleList', vehicleList);
    Alpine.data('vehicleDetail', vehicleDetail);
    Alpine.data('departures', departures);
    Alpine.data('idle', idle);

    idleService.init(Alpine.store('app'));

    // Handle back/forward navigation
    window.addEventListener('popstate', (event) => {
        if (event.state && event.state.view) {
            Alpine.store('app').setView(event.state.view, true);
        } else {
            Alpine.store('app').setView('search', true);
        }
    });

    // Set initial view based on URL
    const initialView = new URLSearchParams(window.location.search).get('view') || 'search';
    Alpine.store('app').setView(initialView, true); // Set view without pushing to history
    // Replace the initial state to have a clean history
    const url = new URL(window.location);
    url.searchParams.set('view', initialView);
    const title = `SOCAR - ${initialView.charAt(0).toUpperCase() + initialView.slice(1)}`;
    history.replaceState({ view: initialView }, title, url);
});

 