import { IDLE_TIMEOUT } from '../config/appConfig.js';

export const idleService = {
    timer: null,
    store: null,
    init(alpineStore) {
        this.store = alpineStore;
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'].forEach(e => {
            document.addEventListener(e, () => this.reset(), true);
        });
        this.reset();
    },
    reset() {
        if (this.store.isIdle) {
            this.store.isIdle = false;
        }
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
            this.store.isIdle = true;
            this.store.setView('search'); // Go to home screen when idle
        }, IDLE_TIMEOUT);
    }
};