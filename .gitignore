 # Node.js
 node_modules/
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
 .pnpm-debug.log*
 
 # Logs
 logs/
 *.log
 
 # Runtime data
 pids/
 *.pid
 *.seed
 *.pid.lock
 
 # Directory for compiled output
 dist/
 build/
 
 # Environment variables
 .env
 .env.local
 .env.development.local
 .env.test.local
 .env.production.local
 
 # macOS
 .DS_Store
 
 # VS Code
 .vscode/
 
 # Editor/IDE specific files
 .idea/
 *.sublime-project
 *.sublime-workspace
 
 # Thumbnails
 .thumbnails/
 
 # Temporary files
 *.tmp
 *~
 
 # Service Worker cache (if not managed by build process)
 # sw.js (if it's generated or dynamically updated and shouldn't be versioned)
 # For this project, sw.js seems to be source code, so it should be tracked.
 # If you later have a build step that generates a new sw.js, you might add it here.